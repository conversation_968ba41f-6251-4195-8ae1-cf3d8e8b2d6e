import {  motion } from "framer-motion"
import Map from "./Map"
import Form from "./Form"

export default function ContactHero() {
    const textVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { duration: 0.8, delay: i * 0.3 }
  })
}
  return (
    <>
    <div className="lg:mx-17 rounded-2xl  bg-gradient-to-r from-[#722973] to-[#a43ca6] md:mx-10 mx-6 lg:py-8 md:py-6 py-5 flex flex-col justify-center items-center">
        <motion.h2
          custom={0}
          variants={textVariants}
          initial="hidden"
          animate="visible"
          className="font-copperplateBold lg:text-7xl md:text-6xl text-5xl font-extrabold  text-white  "
        >
         Contact Us
        </motion.h2>
        <motion.p
          custom={1}
          variants={textVariants}
          initial="hidden"
          animate="visible"
           className="mt-4 text-white font-copperplate">
                  Get in touch with us today and let us help you with any questions or inquiries you may have.
        </motion.p>
    </div>
    <div className="lg:px-17 md:px-10 px-6 lg:py-8 md:py-6 py-5  justify-center items-center flex gap-2.5 mt-10">
        <div className=" w-1/2 p-0.5 bg-gradient-to-r from-[#722973] to-[#F07EF2] via-[#a43ca6]  rounded-2xl h-1/2">
          <Form/>
        </div>
        <div className=" w-1/2   ">
        <Map/>
        </div>
    </div>
    </>

  )
}
