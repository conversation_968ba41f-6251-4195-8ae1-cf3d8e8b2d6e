import image from '../../assets/aboutHero.png'
import { motion } from "framer-motion"

export default function AboutHero() {
    
const textVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { duration: 0.8, delay: i * 0.3 }
  })
}
  return (
    <section className="lg:px-17 md:px-10 px-6 lg:py-8 md:py-6 py-5 relative flex justify-center items-center h-screen">
        <img src={image} alt=""  className=' absolute bottom-0 left-0 w-50'/>
        <img src={image} alt=""  className=' absolute top-0 left-0 opacity-20 w-1/3'/>
        <img src={image} alt=""  className=' absolute bottom-0 right-0  opacity-15 w-1/3'/>
        <img src={image} alt=""  className=' absolute top-20  right-0 w-50'/>
        <div className=' flex flex-col justify-center items-center gap-4 text-center'>
        <motion.h2
          custom={0}
          variants={textVariants}
          initial="hidden"
          animate="visible"
          className="font-copperplateBold lg:text-7xl md:text-6xl text-5xl font-extrabold bg-gradient-to-r from-[#722973] to-[#a43ca6] bg-clip-text text-transparent"
        >
          About Us
        </motion.h2>
        <motion.p
            custom={1}
            variants={textVariants}
            initial="hidden"
            animate="visible"
            className="text-lg  w-1/2 ">
            At <span className=' text-lg font-semibold font-copperplateBold text-[#722973]'>ONE DOOR</span>, we believe digital innovation drives growth and transformation. We provide integrated tech solutions — from websites and apps to e-commerce platforms — turning ideas into reality.

        </motion.p>
        <motion.p
            custom={1}
            variants={textVariants}
            initial="hidden"
            animate="visible"
            className="text-lg  w-1/2 ">
We focus on creativity, security, and seamless user experiences, using the latest digital tools. Our mission is to be a trusted partner, delivering quality, transparency, and long-term success for our clients.
        </motion.p>
        </div>
    </section>


  );
}
