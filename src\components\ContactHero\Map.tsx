import type { LatLngExpression } from "leaflet";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';

export default function Map() {
    const position: LatLngExpression = [36.2056, 37.1542];

  return (
        <MapContainer className=" overflow-hidden rounded-2xl" center={position} zoom={13}  style={{ height: '400px', width: '100%'}} >
          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
          <Marker position={position}>
            <Popup>One Door Company</Popup>
          </Marker>
        </MapContainer>    
  )
}
