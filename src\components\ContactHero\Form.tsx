
import styles from './Form.module.css';

export default function Form() {
  return (
    <div className="bg-white text-black w-full p-6 rounded-2xl h-full space-y-6">
      {/* Full Name and Email Row */}
      <div className="flex justify-between items-center gap-4 w-full">
        <div className="flex flex-col w-1/2">
          <label htmlFor="fullName" className="text-gray-700 font-medium mb-2">Full Name</label>
          <input
            type="text"
            id="fullName"
            placeholder="Type here"
            className="bg-gray-100 text-gray-800 placeholder-gray-500 border border-gray-300 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
        </div>
        <div className="flex flex-col w-1/2">
          <label htmlFor="email" className="text-gray-700 font-medium mb-2">Email</label>
          <input
            type="email"
            id="email"
            placeholder="Type here"
            className="bg-gray-100 text-gray-800 placeholder-gray-500 border border-gray-300 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
        </div>
      </div>

      {/* Why are you contacting us Section */}
      <div className="bg-gray-100 border border-gray-300 rounded-lg p-6">
        <h3 className="text-gray-700 font-medium mb-4">Why are you contacting us?</h3>
        <div className="grid grid-cols-2 gap-4">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input type="checkbox" className="w-5 h-5 text-green-500 bg-white border-gray-300 rounded focus:ring-green-500" defaultChecked />
            <span className="text-gray-700">Web Design</span>
          </label>
          <label className="flex items-center space-x-3 cursor-pointer">
            <input type="checkbox" className="w-5 h-5 text-green-500 bg-white border-gray-300 rounded focus:ring-green-500" />
            <span className="text-gray-700">Collaboration</span>
          </label>
          <label className="flex items-center space-x-3 cursor-pointer">
            <input type="checkbox" className="w-5 h-5 text-green-500 bg-white border-gray-300 rounded focus:ring-green-500" />
            <span className="text-gray-700">Mobile App Design</span>
          </label>
          <label className="flex items-center space-x-3 cursor-pointer">
            <input type="checkbox" className="w-5 h-5 text-green-500 bg-white border-gray-300 rounded focus:ring-green-500" />
            <span className="text-gray-700">Others</span>
          </label>
        </div>
      </div>

      {/* Budget Section */}
      <div className="bg-gray-100 border border-gray-300 rounded-lg p-6">
        <h3 className="text-gray-700 font-medium mb-2">Your Budget</h3>
        <p className="text-gray-500 mb-6">Slide to indicate your budget range</p>
        <div className="relative">
          <input
            type="range"
            min="1000"
            max="10000"
            defaultValue="5000"
            className={`w-full cursor-pointer ${styles.slider}`}
          />
          <div className="flex justify-between text-gray-700 mt-2">
            <span>$1000</span>
            <span>$5000</span>
          </div>
        </div>
      </div>

      {/* Message Section */}
      <div className="bg-gray-100 border border-gray-300 rounded-lg p-6">
        <label htmlFor="message" className="text-gray-700 font-medium mb-4 block">Your Message</label>
        <textarea
          id="message"
          placeholder="Type here"
          rows={4}
          className="w-full bg-white text-gray-800 placeholder-gray-500 border border-gray-300 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"
        />
      </div>

      {/* Submit Button */}
      <div className="flex justify-center">
        <button className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200">
          Submit
        </button>
      </div>
    </div>
  )
}
